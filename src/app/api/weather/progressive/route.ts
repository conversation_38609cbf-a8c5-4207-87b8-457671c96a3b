import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getWeatherForecasts } from '@/lib/weather-service';
import { sampleRoutePoints } from '@/lib/gpx-parser';
import { getCachedForecast, setCachedForecast } from '@/lib/forecast-cache';
import { APIResponse, WeatherResponse, RoutePoint } from '@/types';
import { ERROR_MESSAGES, SUCCESS_MESSAGES, ROUTE_CONFIG } from '@/lib/constants';
import { createErrorHandler, withRetryAndTimeout } from '@/lib/api-error-handler';
import { createValidationMiddleware } from '@/lib/api-validation';
import { weatherRequestValidationSchema } from '@/lib/validation';
import { ValidationError, NetworkError } from '@/lib/error-tracking';

// Progressive loading schema with chunk parameters
const progressiveWeatherSchema = weatherRequestValidationSchema.extend({
  chunk: z.object({
    index: z.number().min(0),
    size: z.number().min(10).max(100).default(50),
    total: z.number().min(1)
  }).optional()
});

const validateProgressiveWeatherRequest = createValidationMiddleware<any, WeatherResponse>(progressiveWeatherSchema);

async function progressiveWeatherHandler(
  validatedData: any,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  _request: NextRequest
): Promise<NextResponse<APIResponse<WeatherResponse>>> {
  const { route, settings, chunk } = validatedData;

  // Apply default settings
  const finalSettings = {
    startTime: new Date(),
    averageSpeed: ROUTE_CONFIG.DEFAULT_SPEED,
    forecastInterval: ROUTE_CONFIG.DEFAULT_INTERVAL,
    units: 'metric' as const,
    timezone: 'UTC',
    ...settings
  };

  // Validate route has sufficient points
  if (!route.points || route.points.length < 2) {
    throw new ValidationError(ERROR_MESSAGES.WEATHER.INVALID_COORDINATES);
  }

  console.log(`Processing progressive weather request for route: ${route.name}`);
  console.log(`Settings: interval=${finalSettings.forecastInterval}km, speed=${finalSettings.averageSpeed}km/h`);

  // Sample route points based on interval
  const sampledPoints = sampleRoutePoints(route, finalSettings.forecastInterval);
  console.log(`Sampled ${sampledPoints.length} points from ${route.points.length} total points`);

  // Calculate time estimates for each point
  const pointsWithTime = sampledPoints.map((point, index) => {
    const timeOffset = (point.distance / finalSettings.averageSpeed) * 60 * 60 * 1000; // Convert hours to milliseconds
    return {
      ...point,
      estimatedTime: new Date(finalSettings.startTime.getTime() + timeOffset)
    };
  });

  // Handle chunked processing for large routes
  let pointsToProcess = pointsWithTime;
  let isPartialResponse = false;
  
  if (chunk) {
    const startIndex = chunk.index * chunk.size;
    const endIndex = Math.min(startIndex + chunk.size, pointsWithTime.length);
    pointsToProcess = pointsWithTime.slice(startIndex, endIndex);
    isPartialResponse = true;
    
    console.log(`Processing chunk ${chunk.index + 1}/${Math.ceil(pointsWithTime.length / chunk.size)}: points ${startIndex}-${endIndex}`);
  } else if (pointsWithTime.length > 50) {
    // Auto-chunk for large routes if no chunk specified
    pointsToProcess = pointsWithTime.slice(0, 50);
    isPartialResponse = true;
    console.log(`Auto-chunking large route: processing first 50 of ${pointsWithTime.length} points`);
  }

  // Check cache for this specific chunk
  const cacheKey = `${route.id}_${finalSettings.forecastInterval}_${chunk?.index || 0}`;
  let cachedForecasts;
  
  try {
    cachedForecasts = await withRetryAndTimeout(
      () => getCachedForecast({ ...route, id: cacheKey }, finalSettings),
      { maxRetries: 1, timeout: 2000 }
    );
  } catch (error) {
    console.warn('Forecast cache lookup failed:', error);
  }

  if (cachedForecasts) {
    console.log(`Cache hit for chunk: ${cacheKey}`);
    return NextResponse.json<APIResponse<WeatherResponse>>({
      success: true,
      data: {
        forecasts: cachedForecasts,
        cacheHit: true,
        message: SUCCESS_MESSAGES.WEATHER_LOADED + ' (from cache)',
        meta: {
          isPartial: isPartialResponse,
          chunkIndex: chunk?.index || 0,
          totalChunks: chunk ? Math.ceil(pointsWithTime.length / chunk.size) : 1,
          totalPoints: pointsWithTime.length,
          processedPoints: pointsToProcess.length
        }
      },
      timestamp: new Date()
    });
  }

  // Get weather forecasts with timeout appropriate for chunk size
  const timeoutMs = Math.min(25000, pointsToProcess.length * 500); // 500ms per point, max 25s
  
  const forecasts = await withRetryAndTimeout(
    async () => {
      try {
        return await getWeatherForecasts(pointsToProcess);
      } catch (error) {
        if (error instanceof Error) {
          if (error.message.includes('rate limit')) {
            throw new NetworkError('Weather API rate limit exceeded. Please try again later.');
          } else if (error.message.includes('API key')) {
            throw new NetworkError('Weather service configuration error.');
          } else if (error.message.includes('network') || error.message.includes('fetch')) {
            throw new NetworkError('Failed to connect to weather service.');
          }
        }
        throw error;
      }
    },
    {
      maxRetries: 2,
      timeout: timeoutMs,
      retryCondition: (error) =>
        !error.message.includes('API key') &&
        !error.message.includes('validation')
    }
  );

  if (forecasts.length === 0) {
    throw new NetworkError(ERROR_MESSAGES.WEATHER.NO_DATA);
  }

  // Cache the chunk results
  try {
    await withRetryAndTimeout(
      () => setCachedForecast({ ...route, id: cacheKey }, finalSettings, forecasts),
      { maxRetries: 1, timeout: 2000 }
    );
  } catch (error) {
    console.warn('Failed to cache forecast results:', error);
  }

  console.log(`Successfully generated ${forecasts.length} weather forecasts for chunk`);

  return NextResponse.json<APIResponse<WeatherResponse>>({
    success: true,
    data: {
      forecasts,
      cacheHit: false,
      message: SUCCESS_MESSAGES.WEATHER_LOADED,
      meta: {
        isPartial: isPartialResponse,
        chunkIndex: chunk?.index || 0,
        totalChunks: chunk ? Math.ceil(pointsWithTime.length / chunk.size) : 1,
        totalPoints: pointsWithTime.length,
        processedPoints: pointsToProcess.length,
        nextChunkIndex: isPartialResponse && (chunk?.index || 0) + 1 < Math.ceil(pointsWithTime.length / (chunk?.size || 50)) 
          ? (chunk?.index || 0) + 1 
          : undefined
      }
    },
    timestamp: new Date()
  });
}

export const POST = createErrorHandler(
  (request: NextRequest) => validateProgressiveWeatherRequest(request, progressiveWeatherHandler)
);

export async function GET() {
  return NextResponse.json({
    message: 'Progressive weather forecast endpoint for large routes.',
    description: 'Processes weather data in chunks to avoid timeouts on large routes.',
    usage: {
      endpoint: '/api/weather/progressive',
      method: 'POST',
      chunkSize: 'Default 50 points per chunk, max 100',
      timeout: 'Adaptive based on chunk size (max 25s)'
    },
    requiredFields: ['route'],
    optionalFields: [
      'settings.forecastInterval', 
      'settings.averageSpeed', 
      'settings.startTime',
      'chunk.index',
      'chunk.size',
      'chunk.total'
    ]
  });
}
