"use client";

import { useState, useCallback, useRef, useEffect } from 'react';
import { FileUpload } from './FileUpload';
import { Route } from '@/types';
import { toast } from 'sonner';

interface SafeFileUploadProps {
  onRouteUploaded: (route: Route) => void;
  isLoading?: boolean;
  className?: string;
}

export function SafeFileUpload({ onRouteUploaded, isLoading = false, className }: SafeFileUploadProps) {
  const [hasError, setHasError] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const timeoutRef = useRef<NodeJS.Timeout>();

  // Reset error state after successful interaction
  const handleRouteUploaded = useCallback((route: Route) => {
    setHasError(false);
    setRetryCount(0);
    onRouteUploaded(route);
  }, [onRouteUploaded]);

  // Error boundary effect
  useEffect(() => {
    const handleError = (event: ErrorEvent) => {
      if (event.filename?.includes('FileUpload') || event.message?.includes('file')) {
        console.error('File upload error caught:', event);
        setHasError(true);
        toast.error('File upload encountered an error. Please refresh the page and try again.');
      }
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      if (event.reason?.message?.includes('file') || event.reason?.message?.includes('upload')) {
        console.error('File upload promise rejection:', event);
        setHasError(true);
        toast.error('File upload failed. Please try again.');
      }
    };

    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const handleRetry = useCallback(() => {
    if (retryCount < 3) {
      setHasError(false);
      setRetryCount(prev => prev + 1);
      
      // Set a timeout to detect if the component hangs
      timeoutRef.current = setTimeout(() => {
        console.warn('File upload component may have hung, showing fallback');
        setHasError(true);
        toast.error('File picker is not responding. Please refresh the page.');
      }, 10000); // 10 second timeout
    } else {
      toast.error('Multiple failures detected. Please refresh the page and try again.');
    }
  }, [retryCount]);

  // Clear timeout when component unmounts or succeeds
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  if (hasError) {
    return (
      <div className="w-full p-8 border-2 border-dashed border-red-300 rounded-lg text-center">
        <div className="text-red-600 mb-4">
          <svg className="h-12 w-12 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
          <h3 className="text-lg font-semibold">File Upload Error</h3>
          <p className="text-sm text-gray-600 mt-2">
            The file picker encountered an issue. This can happen due to browser security settings or extensions.
          </p>
        </div>
        
        <div className="space-y-3">
          {retryCount < 3 ? (
            <button
              type="button"
              onClick={handleRetry}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              Try Again ({3 - retryCount} attempts left)
            </button>
          ) : (
            <button
              type="button"
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
            >
              Refresh Page
            </button>
          )}
          
          <div className="text-xs text-gray-500 mt-4">
            <p><strong>Troubleshooting tips:</strong></p>
            <ul className="text-left mt-2 space-y-1">
              <li>• Try refreshing the page</li>
              <li>• Disable browser extensions temporarily</li>
              <li>• Try a different browser (Chrome, Firefox, Safari)</li>
              <li>• Check if your browser allows file uploads</li>
            </ul>
          </div>
        </div>
      </div>
    );
  }

  return (
    <FileUpload
      onRouteUploaded={handleRouteUploaded}
      isLoading={isLoading}
      className={className}
    />
  );
}
